import { ClientProfileOption } from '@/components/ClientProfileAutoCompleteAsync';
import ClientProfileSelector from '@/components/ClientProfileSelector';
import { Constraint } from '@/components/Dispatch/ConstraintInputGroup';
import ConstraintSelector from '@/components/Dispatch/ConstraintSelector';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import MarketplaceSelector from '@/components/MarketplaceSelector';
import ProcedureSelector, {
  SelectedProcedureItem,
} from '@/components/ProcedureSelector';
import {
  FullAppointmentRequestFieldsFragment,
  MarketplaceFieldsFragment,
  RoleScope,
  useCreateAppointmentRequestMutation,
  useMarketplaceDispatchQuery,
  useUpdateAppointmentRequestMutation,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  LinearProgress,
  TextField,
  Typography,
  useMediaQuery,
} from '@material-ui/core';
import { useTheme } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import { filter, shuffle } from 'lodash';
import { useSnackbar } from 'notistack';
import { useEffect, useMemo, useRef, useState } from 'react';

const editPermission = [
  'marketplaces:full',
  {
    scope: RoleScope.Marketplace,
    permission: ['marketplace.appointments:full'],
  },
];

interface CreateAppointmentRequestDialogProps {
  marketplaces: MarketplaceFieldsFragment[];
  title: string;
  open: boolean;
  onClose: () => void;
  onCreated?:
    | ((appointmentRequest: FullAppointmentRequestFieldsFragment) => void)
    | null;
  editRequest?: FullAppointmentRequestFieldsFragment | null;
}

export default function CreateAppointmentRequestDialog({
  marketplaces,
  title,
  open,
  onClose,
  onCreated = null,
  editRequest = null,
}: CreateAppointmentRequestDialogProps): JSX.Element {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const [marketplaceId, setMarketplaceId] = useState<string | null>(
    editRequest?.marketplace?.id ?? null,
  );

  const [selectedPatients, setSelectedPatients] = useState<
    ClientProfileOption[]
  >(editRequest?.clientProfiles ?? []);

  const [selectedProcedures, setSelectedProcedures] = useState<
    SelectedProcedureItem[]
  >(
    (editRequest?.procedureBaseDefs ?? []).map((def) => ({
      ...def,
      type: 'procedure',
    })),
  );

  const [constraints, setConstraints] = useState<Constraint[]>(
    (editRequest?.constraints ?? []).map((constraint) => ({
      id: +constraint.id,
      organizations: constraint.organizations,
      profiles: constraint.profiles,
      timeRanges: constraint.timeRanges.map((tr) => ({ ...tr, id: +tr.id })),
    })),
  );

  const [location, setLocation] = useState<string>(editRequest?.location ?? '');
  const [initialLocation, setInitialLocation] = useState(location);
  const [notes, setNotes] = useState(editRequest?.notes ?? '');
  const [error, setError] = useState<string[]>([]);

  const [canEdit] = useAuthorize(editPermission, {
    resourceId: marketplaceId,
  });

  const [createAppointmentRequest, createResult] =
    useCreateAppointmentRequestMutation();

  const [updateAppointmentRequest, updateResult] =
    useUpdateAppointmentRequestMutation();

  const loading = createResult.loading || updateResult.loading;

  const { data: marketplaceData } = useMarketplaceDispatchQuery({
    variables: { id: marketplaceId as string },
    skip: !marketplaceId,
  });

  const marketplace = marketplaceData?.marketplace;

  const tzid = selectedPatients[0]?.tzid ?? dayjs.tz.guess();

  const randomOrganization = useMemo(
    () =>
      marketplace?.organizations.length
        ? shuffle(marketplace.organizations)[0]
        : null,
    [marketplace?.organizations],
  );

  const selectedPatientIdRef = useRef(selectedPatients[0]?.id);

  useEffect(() => {
    if (selectedPatientIdRef.current !== selectedPatients[0]?.id) {
      const address = selectedPatients[0]?.address ?? '';
      setLocation(address);
      setInitialLocation(address);
    }

    selectedPatientIdRef.current = selectedPatients[0]?.id;
  }, [selectedPatients]);

  const isValid = Boolean(
    marketplaceId &&
      selectedPatients.length &&
      selectedProcedures.length &&
      location.trim().length &&
      constraints.length,
  );

  const handleClose = () => {
    onClose();
  };

  const handleSubmit = async () => {
    if (!canEdit || loading || !isValid) {
      return;
    }

    const input = {
      clientProfileIds: selectedPatients.map(({ id }) => id),
      procedureBaseDefIds: filter(selectedProcedures, {
        type: 'procedure',
      }).map((p) => p.id),
      location,
      notes,
      constraints: constraints.map((constraint) => ({
        organizationIds: constraint.organizations.map((org) => org.id),
        profileIds: constraint.profiles.map((p) => p.id),
        timeRanges: constraint.timeRanges.map(({ start, end }) => ({
          start,
          end,
        })),
      })),
    };

    if (editRequest) {
      try {
        await updateAppointmentRequest({
          variables: { input: { ...input, id: editRequest.id } },
        });

        enqueueSnackbar('Appointment request updated', { variant: 'success' });
        handleClose();
      } catch (err) {
        setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
      }
    } else {
      try {
        const result = await createAppointmentRequest({
          variables: {
            input: { ...input, marketplaceId: marketplaceId as string },
          },
        });

        enqueueSnackbar('Appointment request created', { variant: 'success' });

        if (onCreated && result.data?.createAppointmentRequest) {
          onCreated(result.data.createAppointmentRequest);
        }

        handleClose();
      } catch (err) {
        setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
      }
    }
  };

  const handleChangeMarketplace = (id: string | null) => {
    setMarketplaceId(id);
    setSelectedPatients([]);
    setSelectedProcedures([]);
    setConstraints([]);
  };

  const handleSelectPatients = (selection: ClientProfileOption[]) => {
    setSelectedPatients(selection);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="create-request-dialog-title"
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <Box position="absolute" width="100%" top={0} left={0}>
        {loading && <LinearProgress />}
      </Box>

      <DialogTitle id="create-request-dialog-title">{title}</DialogTitle>

      <DialogContent dividers>
        <Box my={2}>
          <MarketplaceSelector
            marketplaces={marketplaces}
            selectedId={marketplaceId}
            onChange={handleChangeMarketplace}
            disabled={Boolean(editRequest)}
            isRequired
          />
        </Box>

        <Box my={2}>
          <ClientProfileSelector
            selectedClientProfiles={selectedPatients}
            onChangeSelection={handleSelectPatients}
            queryOptions={
              marketplace && {
                marketplaceGroupIds: [marketplace.groupId],
              }
            }
            createOptions={
              randomOrganization && { organization: randomOrganization }
            }
            disabled={!marketplace?.organizations.length}
            isRequired
            // multiple
          />
        </Box>

        <Box my={2}>
          <ProcedureSelector
            procedures={marketplace?.procedureBaseDefs ?? []}
            selectedProcedures={selectedProcedures}
            onChangeSelection={(value) => setSelectedProcedures(value)}
            disabled={!marketplace?.organizations.length}
            isRequired
          />
        </Box>

        <Box mt={-2}>
          <LocationAutocomplete
            key={initialLocation ?? 'empty'}
            onChange={(value) => setLocation(value)}
            initialValue={initialLocation}
          />
        </Box>

        <Box
          my={2}
          display="flex"
          flexDirection="column"
          alignItems="flex-start"
        >
          <Box mb={1} fontWeight="bold">
            <Typography color="textPrimary">Constraints</Typography>
          </Box>

          <ConstraintSelector
            key={marketplace?.id}
            organizations={marketplace?.organizations ?? []}
            constraints={constraints}
            onChange={(changed) => setConstraints(changed)}
            disabled={!marketplace}
            tzid={tzid}
          />
        </Box>

        <Box my={2}>
          <TextField
            multiline
            fullWidth
            variant="outlined"
            value={notes}
            minRows={5}
            onChange={(e) => setNotes(e.target.value)}
            label="Notes"
          />
        </Box>

        {error.map((errorMessage) => (
          <Box key={errorMessage} mt={1}>
            <Alert variant="outlined" severity="error">
              {errorMessage}
            </Alert>
          </Box>
        ))}
      </DialogContent>

      <DialogActions>
        <Button
          onClick={handleClose}
          color="primary"
          size="large"
          disabled={loading}
        >
          Cancel
        </Button>
        <Button
          variant="text"
          color="primary"
          size="large"
          type="submit"
          onClick={handleSubmit}
          disabled={!isValid || loading}
        >
          {editRequest ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
