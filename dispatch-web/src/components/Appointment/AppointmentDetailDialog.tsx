import AppointmentDialogTabs from '@/components/Appointment/AppointmentDialogTabs';
import BookingStatus from '@/components/Appointment/BookingStatus';
import CreateAppointmentDialog from '@/components/Appointment/CreateAppointmentDialog';
import ShareAppointmentDialog from '@/components/Appointment/ShareAppointmentDialog';
import StartCompleteAppointmentDialog from '@/components/Appointment/StartCompleteAppointmentDialog';
import ChartNotesDialog from '@/components/ChartNotesDialog';
import AppointmentCheckoutContent from '@/components/Checkout/CheckoutContent';
import ClientProfileCard from '@/components/ClientProfileCard';
import ConfirmDialog from '@/components/ConfirmDialog';
import InlineAlert from '@/components/InlineAlert';
import PractitionerCard from '@/components/PractitionerCard';
import ProcedureCard from '@/components/ProcedureCard';
import ExamStatusChip from '@/components/Qualiphy/ExamStatusChip';
import StandardDialogTitle from '@/components/StandardDialogTitle';
import {
  AppointmentDocument,
  AppointmentStatus,
  FullAppointmentFieldsFragment,
  FullClientProfileFieldsFragment,
  FullOrganizationFragment,
  FullProcedureDefFieldsFragment,
  ParticipantType,
  ProcedureDefFieldsFragment,
  ProfileFieldsFragment,
  QualiphyInvitationStatus,
  RoleScope,
  useArchiveAppointmentMutation,
  useCancelAppointmentMutation,
  useResendQualiphyExamInviteMutation,
  useResetAppointmentMutation,
  useSendQualiphyExamInviteMutation,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { useLayoutStyles } from '@/hooks/styles';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import { fullName } from '@/utils/common';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Checkbox,
  Dialog,
  DialogActions,
  DialogContent,
  Divider,
  FormControlLabel,
  IconButton,
  Menu,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
  makeStyles,
  useMediaQuery,
} from '@material-ui/core';
import { useTheme } from '@material-ui/core/styles';
import { CSSProperties } from '@material-ui/core/styles/withStyles';
import ArchiveIcon from '@material-ui/icons/Archive';
import AssignmentIcon from '@material-ui/icons/AssignmentOutlined';
import CancelIcon from '@material-ui/icons/Cancel';
import CopyIcon from '@material-ui/icons/ContentCopy';
import ErrorIcon from '@material-ui/icons/ErrorOutline';

import ResetIcon from '@material-ui/icons/RestartAlt';
import ShareIcon from '@material-ui/icons/Share';
import SupportIcon from '@material-ui/icons/SupportAgent';
import TaskAltIcon from '@material-ui/icons/TaskAlt';
import { Alert, Skeleton } from '@material-ui/lab';
import { filter, flatten, maxBy, uniqBy } from 'lodash';
import Link from 'next/link';
import { useSnackbar } from 'notistack';
import React, { useMemo, useState } from 'react';

const useStyles = makeStyles((theme) => ({
  sectionTitle: {
    ...theme.typography.body1,
    fontWeight: theme.typography.fontWeightBold as CSSProperties['fontWeight'],
    marginBottom: theme.spacing(0.5),
  },
  qualiphyInput: {
    fontSize: theme.typography.body2.fontSize,
  },
  qualiphyStatus: {
    ...theme.typography.body2,
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(0.5),
  },
}));

const editPermission = [
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:full'],
  },
];

const editSelfPermission = [
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:self'],
  },
];

interface AppointmentDetailDialogProps {
  open: boolean;
  onClose: (reason?: 'archived') => void;
  appointment?: FullAppointmentFieldsFragment | null;
  organization?: FullOrganizationFragment | null;
  onCopyAppointment?: () => void;
}

export default function AppointmentDetailDialog({
  open,
  onClose,
  appointment = null,
  organization = null,
  onCopyAppointment,
}: AppointmentDetailDialogProps): JSX.Element {
  const classes = useStyles();
  const layoutClasses = useLayoutStyles();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const [cancelErrors, setCancelErrors] = useState<string[]>([]);
  const [archiveErrors, setArchiveErrors] = useState<string[]>([]);
  const [resetErrors, setResetErrors] = useState<string[]>([]);
  const [qualiphyErrors, setQualiphyErrors] = useState<string[]>([]);

  const [tab, setTab] = useState('info');
  const [editing, setEditing] = useState(false);
  const [confirmQualiphyOpen, setConfirmQualiphyOpen] = useState(false);
  const [confirmQualiphyResendOpen, setConfirmQualiphyResendOpen] =
    useState(false);
  const [menuAnchor, setMenuAnchor] = useState<HTMLElement | null>(null);
  const [confirmCancelOpen, setConfirmCancelOpen] = useState(false);
  const [confirmArchiveOpen, setConfirmArchiveOpen] = useState(false);
  const [confirmResetOpen, setConfirmResetOpen] = useState(false);
  const [startCompleteOpen, setStartCompleteOpen] = useState(false);
  const [showChartNotes, setShowChartNotes] = useState(false);
  const [sharingAppointment, setSharingAppointment] = useState(false);
  const [refund, setRefund] = useState(false);

  const { profile } = useProfile();

  const [canEditFull] = useAuthorize(editPermission, {
    resourceId: organization?.id,
  });

  const [canEditSelf] = useAuthorize(editSelfPermission, {
    resourceId: organization?.id,
  });

  const [cancelAppointment, { loading: loadingCancel }] =
    useCancelAppointmentMutation();

  const [archiveAppointment, { loading: loadingArchive }] =
    useArchiveAppointmentMutation();

  const [resetAppointment, { loading: loadingReset }] =
    useResetAppointmentMutation();

  const [sendQualiphyInvitation, { loading: loadingQualipy }] =
    useSendQualiphyExamInviteMutation();

  const [resendQualiphyExamInvite, { loading: loadingResend }] =
    useResendQualiphyExamInviteMutation();

  const qualiphyEnabled = organization?.qualiphyEnabled ?? false;

  const loading =
    loadingCancel ||
    loadingArchive ||
    loadingReset ||
    loadingQualipy ||
    loadingResend;

  const patientParticipants = useMemo(
    () =>
      appointment?.participants.filter(
        (p) => p.type === ParticipantType.Patient,
      ) ?? [],
    [appointment],
  );

  const practitionerParticipants =
    appointment?.participants.filter(
      (p) => p.type === ParticipantType.Practitioner,
    ) ?? [];

  const procedureDefs = (appointment?.procedureBaseDefs ?? [])
    .map(
      (baseDef) =>
        (organization?.procedureDefs ?? []).find((def) =>
          def.baseDefinitions.map(({ id }) => id).includes(baseDef.id),
        ) ?? baseDef,
    )
    .filter((def) => def);

  const tzid = organization?.tzid ?? dayjs.tz.guess();

  const startDate = appointment?.start
    ? dayjs(appointment.start).tz(tzid).format('ddd, ll')
    : '';

  const startTime = appointment?.start
    ? dayjs(appointment.start).tz(tzid).format('h:mm A')
    : '';

  const endTime = appointment?.end
    ? dayjs(appointment.end).tz(tzid).format('h:mm A z')
    : '';

  const invitation = appointment?.qualiphyInvitation;

  const hasPendingExams =
    invitation?.exams?.some(
      (exam) => exam.status === QualiphyInvitationStatus.Pending,
    ) ?? false;

  const canEdit =
    canEditFull ||
    (canEditSelf &&
      practitionerParticipants.find((p) => p.participant?.id === profile?.id));

  const canEditBooked =
    !!appointment &&
    !appointment.archivedAt &&
    canEdit &&
    ![AppointmentStatus.Completed, AppointmentStatus.Cancelled].includes(
      appointment.status,
    );

  const canStart =
    canEditBooked &&
    appointment.status === AppointmentStatus.Booked &&
    !appointment.startedAt;

  const canComplete =
    canEditBooked &&
    appointment.status === AppointmentStatus.Booked &&
    appointment.startedAt;

  const canArchive =
    !!appointment &&
    !appointment.archivedAt &&
    canEditFull &&
    appointment.status === AppointmentStatus.Cancelled;

  const canReset =
    !!appointment &&
    !appointment.archivedAt &&
    (appointment.startedAt || appointment.completedAt) &&
    canEdit &&
    [AppointmentStatus.Booked, AppointmentStatus.Completed].includes(
      appointment.status,
    );

  const clientProfile = useMemo(
    () =>
      patientParticipants?.[0]?.participant as FullClientProfileFieldsFragment,
    [patientParticipants],
  );

  const forms = patientParticipants?.[0]?.forms;
  const signedConsentForms = clientProfile?.patient?.signedConsentForms;

  // create a unique set of consent forms and match up with the most recent signature
  const consentForms = useMemo(
    () =>
      uniqBy(
        flatten(
          (procedureDefs as never as FullProcedureDefFieldsFragment[]).map(
            (def) => def.consentForms ?? [],
          ),
        ),
        (def) => def.id,
      ).map((consentForm) => ({
        consentForm,
        signature: maxBy(
          filter(signedConsentForms, {
            documentId: consentForm.id,
            version: consentForm.version,
          }),
          'signedAt',
        ),
      })),
    [procedureDefs, signedConsentForms],
  );

  const handleClose: AppointmentDetailDialogProps['onClose'] = (reason) => {
    setEditing(false);
    setCancelErrors([]);
    setArchiveErrors([]);
    setResetErrors([]);
    setQualiphyErrors([]);
    onClose(reason);
  };

  const handleChartNotesClose = () => {
    setShowChartNotes(false);
  };

  const handleCancelAppointment = async () => {
    if (!appointment || !canEditBooked) {
      return;
    }

    try {
      await cancelAppointment({
        variables: {
          input: {
            appointmentId: appointment.id,
            refundCheckout: refund,
          },
        },
      });

      enqueueSnackbar('Appointment cancelled', {
        variant: 'success',
      });

      setConfirmCancelOpen(false);
    } catch (err) {
      setCancelErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleArchiveAppointment = async () => {
    if (!appointment || !canArchive) {
      return;
    }

    try {
      await archiveAppointment({
        variables: {
          input: { appointmentId: appointment.id },
        },
      });

      enqueueSnackbar('Appointment archived', {
        variant: 'info',
      });

      setConfirmArchiveOpen(false);
      handleClose('archived');
    } catch (err) {
      setArchiveErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleResetAppointment = async () => {
    if (!appointment || !canReset) {
      return;
    }

    try {
      await resetAppointment({
        variables: {
          input: { appointmentId: appointment.id },
        },
      });

      enqueueSnackbar('Appointment reset', {
        variant: 'info',
      });

      setConfirmResetOpen(false);
    } catch (err) {
      setResetErrors(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
    }
  };

  const handleSendQualiphyInvitation = async () => {
    if (!appointment || loading || !qualiphyEnabled || invitation) {
      return;
    }

    try {
      const { data, errors } = await sendQualiphyInvitation({
        variables: {
          id: appointment.id,
        },
        refetchQueries: [
          { query: AppointmentDocument, variables: { id: appointment.id } },
        ],
      });

      if (data) {
        enqueueSnackbar('Invitation sent', { variant: 'success' });
        setConfirmQualiphyOpen(false);
      }

      if (errors)
        setQualiphyErrors(
          formatGraphQLErrors(errors) ?? [
            'There was an error sending the invitation.',
          ],
        );
    } catch (err) {
      setQualiphyErrors(
        formatGraphQLErrors(err.graphQLErrors) ?? [
          'There was an error sending the invitation.',
        ],
      );
    }
  };

  const handleResendQualiphyInvitation = async () => {
    if (!appointment || loading || !qualiphyEnabled || !invitation) {
      return;
    }

    try {
      const { data, errors } = await resendQualiphyExamInvite({
        variables: {
          id: invitation.id,
        },
        refetchQueries: [
          { query: AppointmentDocument, variables: { id: appointment.id } },
        ],
      });

      if (data?.resendQualiphyExamInvite.id) {
        enqueueSnackbar('Invitation resent', { variant: 'success' });
        setConfirmQualiphyResendOpen(false);
      }

      if (errors)
        setQualiphyErrors(
          formatGraphQLErrors(errors) ?? [
            'There was an error resending the invitation.',
          ],
        );
    } catch (err) {
      setQualiphyErrors(
        formatGraphQLErrors(err.graphQLErrors) ?? [
          'There was an error resending the invitation.',
        ],
      );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => handleClose()}
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <StandardDialogTitle
        fullScreen={fullScreen}
        onClose={() => handleClose()}
        {...(canEditFull &&
          appointment && {
            onClickEdit: () => setEditing(true),
          })}
        {...(canEdit &&
          appointment && {
            onClickContext: (event: React.MouseEvent<HTMLElement>) =>
              setMenuAnchor(event.currentTarget),
          })}
      >
        <Box mb={-3}>
          <div>Appointment Details</div>
          {appointment?.status ? (
            <BookingStatus variant={appointment?.status} />
          ) : (
            <Skeleton width="70px" />
          )}

          <AppointmentDialogTabs tab={tab} setTab={(t) => setTab(t)} />
        </Box>
      </StandardDialogTitle>

      {tab === 'checkout' &&
        !!appointment &&
        (appointment.checkout ? (
          <AppointmentCheckoutContent
            checkout={appointment?.checkout}
            pending={!appointment?.id}
            fullScreen={fullScreen}
          />
        ) : (
          <DialogContent dividers>
            <Alert severity="warning">
              Payments are not enabled for this appointment
            </Alert>
          </DialogContent>
        ))}

      {tab === 'info' && (
        <DialogContent dividers>
          {!appointment ? (
            <Box pb="400px">
              <Skeleton width="65%" />
              <Skeleton width="65%" />
              <Skeleton width="35%" />
            </Box>
          ) : (
            <>
              <Box mt={1} mb={1}>
                {clientProfile && (
                  <>
                    <ClientProfileCard
                      id={clientProfile.id}
                      name={fullName([
                        clientProfile.givenName,
                        clientProfile.familyName,
                      ])}
                      email={clientProfile.email}
                      phone={clientProfile.phone}
                      dateOfBirth={clientProfile.dob}
                      membership={clientProfile.membership}
                      internalNotes={clientProfile.internalNotes}
                      allowEditNotes={Boolean(canEdit)}
                    />
                    <Box
                      mt={1}
                      ml={7}
                      display="flex"
                      alignItems="center"
                      flexWrap="wrap"
                    >
                      <Button
                        variant="outlined"
                        onClick={() => setShowChartNotes(true)}
                        startIcon={<AssignmentIcon />}
                        style={{ marginRight: 8, marginBottom: 8 }}
                      >
                        Chart Notes
                      </Button>
                      {qualiphyEnabled && !invitation && (
                        <Button
                          variant="outlined"
                          onClick={() => setConfirmQualiphyOpen(true)}
                          startIcon={<SupportIcon />}
                          style={{ marginRight: 8, marginBottom: 8 }}
                        >
                          Qualiphy
                        </Button>
                      )}
                      {qualiphyEnabled && invitation && hasPendingExams && (
                        <Button
                          variant="outlined"
                          onClick={() => setConfirmQualiphyResendOpen(true)}
                          startIcon={<SupportIcon />}
                          style={{ marginRight: 8, marginBottom: 8 }}
                        >
                          Resend Qualiphy
                        </Button>
                      )}
                      {patientParticipants?.[0]?.orderApproved === false && (
                        <Box ml={1} mb={1}>
                          <InlineAlert
                            icon={
                              <ErrorIcon
                                style={{ color: theme.palette.error.main }}
                              />
                            }
                            message="Order needed"
                          />
                        </Box>
                      )}
                      {patientParticipants?.[0]?.orderApproved === true && (
                        <Box ml={1} mb={1}>
                          <InlineAlert
                            icon={
                              <TaskAltIcon
                                style={{ color: theme.palette.success.main }}
                              />
                            }
                            message="Order approved"
                          />
                        </Box>
                      )}
                    </Box>
                  </>
                )}
              </Box>

              <Divider />

              <Box my={2}>
                {procedureDefs.map((procedure) => (
                  <Box my={1} key={procedure.id}>
                    <ProcedureCard
                      name={procedure.name}
                      duration={procedure.duration}
                      price={(procedure as ProcedureDefFieldsFragment).price}
                    />
                  </Box>
                ))}
              </Box>

              <Divider />

              <Box my={2}>
                <Typography className={classes.sectionTitle}>
                  {startDate}
                </Typography>
                <Typography variant="body2">
                  {startTime} - {endTime}
                </Typography>
              </Box>

              <Divider />

              <Box my={2}>
                {practitionerParticipants.map((practitioner) => {
                  const participant =
                    practitioner.participant as ProfileFieldsFragment;
                  return (
                    <PractitionerCard
                      key={participant.id}
                      name={fullName(
                        [participant.givenName, participant.familyName],
                        participant.title,
                      )}
                      status={practitioner.status}
                      organizationName={organization?.name}
                    />
                  );
                })}
              </Box>

              {consentForms.length > 0 && (
                <>
                  <Divider />

                  <Box my={2}>
                    <Typography className={classes.sectionTitle}>
                      Consent Forms
                    </Typography>
                    {consentForms.map(({ consentForm, signature }) => (
                      <Box key={consentForm.id} display="flex" my={0.5}>
                        <Box flex="1">
                          <Typography variant="body1" component="div">
                            {consentForm.label}
                          </Typography>

                          <Box flex="1">
                            {signature ? (
                              <InlineAlert
                                icon={<TaskAltIcon />}
                                message={`Signed ${dayjs(
                                  signature.signedAt,
                                ).format(' L, LT')}`}
                              />
                            ) : (
                              <InlineAlert
                                icon={<ErrorIcon color="secondary" />}
                                message="Signature needed"
                              />
                            )}
                          </Box>
                        </Box>
                        <Box display="flex">
                          <div>
                            {signature?.url ? (
                              <Link href={signature.url} passHref>
                                <Button
                                  variant="outlined"
                                  component="a"
                                  target="_blank"
                                >
                                  View signed
                                </Button>
                              </Link>
                            ) : (
                              <Link href={consentForm.url} passHref>
                                <Button
                                  variant="outlined"
                                  component="a"
                                  target="_blank"
                                >
                                  View form
                                </Button>
                              </Link>
                            )}
                          </div>
                        </Box>
                      </Box>
                    ))}
                  </Box>
                </>
              )}

              <Divider />

              {qualiphyEnabled && invitation && (
                <>
                  <Box my={2}>
                    <Typography className={classes.sectionTitle}>
                      Qualiphy Assessment
                    </Typography>

                    {invitation.meetingUrl && (
                      <Box
                        sx={{ my: 1, display: 'flex', alignItems: 'center' }}
                      >
                        <TextField
                          name="meetingUrl"
                          value={invitation.meetingUrl}
                          variant="outlined"
                          size="small"
                          fullWidth
                          inputProps={{ readOnly: true }}
                          onFocus={(event) => event.target.select()}
                          InputProps={{
                            classes: { root: classes.qualiphyInput },
                          }}
                        />
                        <Box>
                          <Tooltip title="Copy link" placement="top">
                            <IconButton
                              size="small"
                              onClick={() => {
                                navigator.clipboard.writeText(
                                  invitation.meetingUrl ?? '',
                                );
                                enqueueSnackbar(
                                  'Meeting URL copied to clipboard',
                                  {
                                    variant: 'info',
                                  },
                                );
                              }}
                              style={{ marginLeft: 8 }}
                            >
                              <CopyIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    )}

                    {invitation.exams?.map((e) => (
                      <Box key={e.id} className={classes.qualiphyStatus}>
                        <ExamStatusChip status={e.status} />
                        {e.title}
                      </Box>
                    ))}
                  </Box>
                  <Divider />
                </>
              )}

              <Box my={2}>
                <Typography className={classes.sectionTitle}>
                  Location
                </Typography>
                <Typography variant="body2">{appointment?.location}</Typography>
              </Box>

              <Divider />

              <Box my={2}>
                <Typography className={classes.sectionTitle}>Notes</Typography>

                {appointment?.notes?.trim() ? (
                  <Typography variant="body2">
                    {appointment.notes.split('\n').map((line) => (
                      <React.Fragment key={line}>
                        {line}
                        <br />
                      </React.Fragment>
                    ))}
                  </Typography>
                ) : (
                  <Typography variant="body2" color="textSecondary">
                    <em>None</em>
                  </Typography>
                )}
              </Box>
            </>
          )}
        </DialogContent>
      )}

      {tab === 'info' && canEditBooked && (
        <DialogActions>
          <Box width="100%" display="flex" justifyContent="space-between">
            <Button
              variant="text"
              color="secondary"
              type="submit"
              onClick={() => setConfirmCancelOpen(true)}
            >
              Cancel appointment
            </Button>
            <Box display="flex">
              {canStart && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  onClick={() => setStartCompleteOpen(true)}
                >
                  Start appointment
                </Button>
              )}
              {canComplete && (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  onClick={() => setStartCompleteOpen(true)}
                >
                  End appointment
                </Button>
              )}
            </Box>
          </Box>
        </DialogActions>
      )}

      {editing && organization && (
        <CreateAppointmentDialog
          title="Edit Appointment"
          open={editing}
          onClose={() => setEditing(false)}
          organization={organization}
          editAppointment={appointment}
        />
      )}

      {showChartNotes &&
        appointment &&
        clientProfile &&
        forms &&
        organization && (
          <ChartNotesDialog
            open={showChartNotes}
            onClose={handleChartNotesClose}
            appointment={appointment}
            clientProfile={clientProfile}
            forms={forms}
            organization={organization}
          />
        )}

      <Menu
        open={Boolean(menuAnchor)}
        onClose={() => {
          setMenuAnchor(null);
        }}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        getContentAnchorEl={null}
        anchorEl={menuAnchor}
        keepMounted
      >
        <MenuItem
          disabled={loading}
          onClick={() => {
            setSharingAppointment(true);
            setMenuAnchor(null);
          }}
        >
          <ShareIcon className={layoutClasses.menuIcon} />
          Share
        </MenuItem>

        <MenuItem
          disabled={loading}
          onClick={() => {
            if (onCopyAppointment) {
              onCopyAppointment();
            }
            setMenuAnchor(null);
          }}
        >
          <CopyIcon className={layoutClasses.menuIcon} />
          Copy Appointment
        </MenuItem>

        {appointment?.status !== AppointmentStatus.Cancelled ? (
          <span>
            <Divider className={layoutClasses.menuDivider} />
            <MenuItem
              disabled={!canEditBooked || loading}
              onClick={() => {
                setConfirmCancelOpen(true);
                setMenuAnchor(null);
              }}
            >
              <CancelIcon className={layoutClasses.menuIcon} />
              Cancel appointment
            </MenuItem>
          </span>
        ) : (
          <MenuItem
            disabled={!canArchive || loading}
            onClick={() => {
              setConfirmArchiveOpen(true);
              setMenuAnchor(null);
            }}
          >
            <ArchiveIcon className={layoutClasses.menuIcon} />
            Archive appointment
          </MenuItem>
        )}

        {canReset && (
          <MenuItem
            disabled={!canReset || loading}
            onClick={() => {
              setConfirmResetOpen(true);
              setMenuAnchor(null);
            }}
          >
            <ResetIcon className={layoutClasses.menuIcon} />
            Reset appointment
          </MenuItem>
        )}
      </Menu>

      <ConfirmDialog
        submitting={loading}
        title="Cancel this appointment?"
        cancelText="Nevermind"
        open={confirmCancelOpen}
        onConfirm={handleCancelAppointment}
        onCancel={() => setConfirmCancelOpen(false)}
        errors={cancelErrors}
        disableTypography
        description={
          appointment?.checkout?.paid ? (
            <FormControlLabel
              control={
                <Checkbox
                  checked={refund}
                  onChange={(_, checked) => setRefund(checked)}
                  name="refund"
                  color="primary"
                />
              }
              label="Refund checkout"
            />
          ) : undefined
        }
      />

      <ConfirmDialog
        submitting={loading}
        title="Archive this appointment?"
        confirmText="Archive"
        open={confirmArchiveOpen}
        onConfirm={handleArchiveAppointment}
        onCancel={() => setConfirmArchiveOpen(false)}
        errors={archiveErrors}
        ConfirmButtonProps={{
          color: 'secondary',
        }}
      />

      <ConfirmDialog
        submitting={loading}
        title="Send to Qualiphy?"
        description="This will send a link to the patient's email and they will be assessed by a practitioner."
        confirmText="Send"
        open={confirmQualiphyOpen}
        onConfirm={handleSendQualiphyInvitation}
        onCancel={() => setConfirmQualiphyOpen(false)}
        errors={qualiphyErrors}
      />

      <ConfirmDialog
        submitting={loading}
        title="Resend Qualiphy invitation?"
        confirmText="Resend"
        open={confirmQualiphyResendOpen}
        onConfirm={handleResendQualiphyInvitation}
        onCancel={() => setConfirmQualiphyResendOpen(false)}
        errors={qualiphyErrors}
      />

      <ConfirmDialog
        submitting={loading}
        title="Reset appointment"
        confirmText="Reset"
        description="Reset to a booked, unstarted appointment?"
        open={confirmResetOpen}
        onConfirm={handleResetAppointment}
        onCancel={() => setConfirmResetOpen(false)}
        errors={resetErrors}
      />

      {!!appointment && (
        <StartCompleteAppointmentDialog
          key={`StartCompleteAppointmentDialog-${Boolean(startCompleteOpen)}`}
          open={startCompleteOpen}
          onClose={() => setStartCompleteOpen(false)}
          appointment={appointment}
        />
      )}

      <ShareAppointmentDialog
        participant={practitionerParticipants[0]}
        procedureDefs={procedureDefs}
        startDate={startDate}
        startTime={startTime}
        endTime={endTime}
        appointment={appointment}
        fullScreen={fullScreen}
        sharingAppointment={sharingAppointment}
        setSharingAppointment={setSharingAppointment}
      />
    </Dialog>
  );
}
