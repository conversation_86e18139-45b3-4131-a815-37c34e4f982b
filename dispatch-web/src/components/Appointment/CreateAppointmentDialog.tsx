import TimeRangeEditor from '@/components/Appointment/TimeRangeEditor';
import { ClientProfileOption } from '@/components/ClientProfileAutoCompleteAsync';
import ClientProfileSelector from '@/components/ClientProfileSelector';
import LocationAutocomplete from '@/components/LocationAutocomplete';
import MarketplaceSelector from '@/components/MarketplaceSelector';
import PractitionerSelector from '@/components/PractitionerSelector';
import ProcedureSelector, {
  SelectedProcedureItem,
} from '@/components/ProcedureSelector';
import {
  FullAppointmentFieldsFragment,
  FullOrganizationFragment,
  ParticipantType,
  RoleScope,
  useCreateAppointmentMutation,
  useUpdateAppointmentMutation,
} from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { formatGraphQLErrors } from '@/utils/apollo-errors';
import { ceilDate } from '@/utils/common';
import dayjs from '@/utils/dayjs';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  LinearProgress,
  TextField,
  useMediaQuery,
} from '@material-ui/core';
import { useTheme } from '@material-ui/core/styles';
import { Alert } from '@material-ui/lab';
import { filter, find } from 'lodash';
import { useSnackbar } from 'notistack';
import { useEffect, useMemo, useRef, useState } from 'react';

const editPermission = [
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:full'],
  },
];

const editSelfPermission = [
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:self'],
  },
];

interface CreateAppointmentDialogProps {
  title: string;
  open: boolean;
  onClose: () => void;
  onCreated?: ((appointment: FullAppointmentFieldsFragment) => void) | null;
  organization: FullOrganizationFragment;
  editAppointment?: FullAppointmentFieldsFragment | null;
}

export default function CreateAppointmentDialog({
  title,
  open,
  onClose,
  onCreated = null,
  organization,
  editAppointment = null,
}: CreateAppointmentDialogProps): JSX.Element {
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const { enqueueSnackbar } = useSnackbar();

  const mapFromBaseDef = (baseDefId: string) =>
    organization.procedureDefs.find((def) =>
      def.baseDefinitions.map(({ id }) => id).includes(baseDefId),
    )?.id;

  const [marketplaceId, setMarketplaceId] = useState(
    editAppointment?.procedureBaseDefs[0]?.marketplaceId ?? null,
  );

  const [selectedPatients, setSelectedPatients] = useState(
    (editAppointment?.participants ?? [])
      .filter((p) => p.participant && p.type === ParticipantType.Patient)
      .map((p) => p.participant) as ClientProfileOption[],
  );

  const [selectedPractitioners, setSelectedPractitioners] = useState(
    (editAppointment?.participants ?? [])
      .filter((p) => p.type === ParticipantType.Practitioner)
      .map((p) => p.participant?.id)
      .filter((id) => id) as string[],
  );

  const [selectedProcedures, setSelectedProcedures] = useState(
    (editAppointment?.procedureBaseDefs ?? [])
      .map(({ id }) => ({ id: mapFromBaseDef(id), type: 'procedure' }))
      .filter(({ id }) => id) as Array<SelectedProcedureItem>,
  );

  const [startTime, setStartTime] = useState(
    new Date(editAppointment?.start ?? ceilDate(Date.now(), 15)),
  );

  const [endTime, setEndTime] = useState(
    new Date(
      editAppointment?.end ?? ceilDate(dayjs().add(1, 'hour').toDate(), 15),
    ),
  );

  const [location, setLocation] = useState(editAppointment?.location ?? '');
  const [initialLocation, setInitialLocation] = useState(location);
  const [notes, setNotes] = useState(editAppointment?.notes ?? '');
  const [error, setError] = useState<string[]>([]);

  const { profile } = useProfile();

  const [canEditFull] = useAuthorize(editPermission, {
    resourceId: organization?.id,
  });

  const [canEditSelf] = useAuthorize(editSelfPermission, {
    resourceId: organization?.id,
  });

  const practitioners =
    editAppointment?.participants
      .filter((p) => p.type === ParticipantType.Practitioner)
      .map((p) => p.participant) ?? [];

  const canEdit =
    canEditFull ||
    (canEditSelf && practitioners.find((p) => p && p.id === profile?.id));

  const marketplace =
    (marketplaceId && find(organization.marketplaces, { id: marketplaceId })) ||
    null;

  const procedureDefs = useMemo(
    () =>
      organization.procedureDefs
        .map((def) => {
          const baseDefinitions = marketplaceId
            ? filter(def.baseDefinitions, { marketplaceId })
            : [];
          const duration = baseDefinitions[0]?.duration ?? def.duration;
          return { ...def, duration, baseDefinitions };
        })
        .filter((def) => def.baseDefinitions.length),
    [organization, marketplaceId],
  );

  const [createAppointment, { loading: loadingCreate }] =
    useCreateAppointmentMutation();

  const [updateAppointment, { loading: loadingUpdate }] =
    useUpdateAppointmentMutation();

  const selectedPatientIdRef = useRef(selectedPatients[0]?.id);

  useEffect(() => {
    if (selectedPatientIdRef.current !== selectedPatients[0]?.id) {
      const address = selectedPatients[0]?.address ?? '';
      setLocation(address);
      setInitialLocation(address);
    }

    selectedPatientIdRef.current = selectedPatients[0]?.id;
  }, [selectedPatients]);

  const submitting = loadingCreate || loadingUpdate;
  const validationErrors = [];

  if (startTime && endTime && startTime >= endTime) {
    validationErrors.push('The end time must come after the start time');
  }

  const isValid =
    Boolean(marketplaceId) &&
    selectedPatients.length > 0 &&
    selectedPractitioners.length > 0 &&
    selectedProcedures.length > 0 &&
    location.trim().length > 0 &&
    Boolean(startTime) &&
    Boolean(endTime) &&
    !validationErrors.length;

  const handleChangeMarketplace = (id: string | null) => {
    setMarketplaceId(id);
    setSelectedProcedures([]);
  };

  const handleChangeStartTime = (start: Date) => {
    const duration = selectedProcedures
      .map(({ id }) => find(procedureDefs, { id })?.duration ?? 0)
      .reduce((a, c) => a + c, 0);

    setStartTime(start);
    setEndTime(
      dayjs(start)
        .add(duration || 60, 'minutes')
        .toDate(),
    );
  };

  const handleSelectPatients = (value: ClientProfileOption[]) => {
    setSelectedPatients(value);
  };

  function handleClose() {
    onClose();
  }

  async function handleSubmit() {
    if (!canEdit || submitting || !isValid) {
      return;
    }

    const ids = [
      ...selectedProcedures
        .filter((item) => item.type === 'procedure')
        .map((item) => item.id),
    ];

    selectedProcedures
      .filter((item) => item.type === 'profile')
      .map((proc) => {
        proc.selected?.map((id) => {
          if (ids.length === 0) {
            ids.push(id);
          } else if (ids.indexOf(id) < 0) {
            ids.push(id);
          }
          return null;
        });
        return null;
      });

    const procedureBaseDefIds = ids
      .map((id) => find(procedureDefs, { id }))
      .map((def) =>
        find(def?.baseDefinitions ?? [], {
          marketplaceId: marketplaceId as string,
        }),
      )
      .map((baseDef) => baseDef?.id)
      .filter((id) => id) as string[];

    const input = {
      start: startTime,
      end: endTime,
      location,
      notes,
      participants: [
        ...selectedPatients.map(({ id }) => ({
          type: ParticipantType.Patient,
          id,
        })),
        ...selectedPractitioners.map((id) => ({
          type: ParticipantType.Practitioner,
          id,
        })),
      ],
      procedureBaseDefIds,
    };

    if (editAppointment) {
      try {
        const { errors } = await updateAppointment({
          variables: {
            input: {
              ...input,
              id: editAppointment.id,
            },
          },
        });

        if (errors?.length) {
          setError(
            formatGraphQLErrors(errors) ?? ['Error updating the appointment'],
          );
        } else {
          enqueueSnackbar('Appointment updated', { variant: 'success' });
          handleClose();
        }
      } catch (err) {
        setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
      }
    } else {
      try {
        const result = await createAppointment({
          variables: {
            input: {
              ...input,
              createdBy: profile?.id as string,
            },
          },
          // update: (store, response) => {
          //   const appointment = response.data?.createAppointment;

          //   if (appointment) {
          //     try {
          //       const data = cloneDeep(
          //         store.readQuery<OrganizationAppointmentsQuery>({
          //           query: OrganizationAppointmentsDocument,
          //           variables: { id: organization.id },
          //         }),
          //       );

          //       if (data?.organization) {
          //         data.organization.appointments = [
          //           ...data.organization.appointments,
          //           appointment,
          //         ];

          //         store.writeQuery<OrganizationAppointmentsQuery>({
          //           query: OrganizationAppointmentsDocument,
          //           variables: { id: organization.id },
          //           data: { ...data },
          //         });
          //       }
          //     } catch {
          //       // might get here if the organization query hasn't been cached yet
          //     }
          //   }
          // },
        });

        enqueueSnackbar('Appointment created', { variant: 'success' });

        if (onCreated && result.data?.createAppointment) {
          onCreated(result.data.createAppointment);
        }

        handleClose();
      } catch (err) {
        setError(formatGraphQLErrors(err.graphQLErrors) ?? [err.message]);
      }
    }
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      aria-labelledby="create-appointment-dialog-title"
      fullScreen={fullScreen}
      maxWidth="sm"
      fullWidth
    >
      <Box position="absolute" width="100%" top={0} left={0}>
        {submitting && <LinearProgress />}
      </Box>

      <DialogTitle id="create-appointment-dialog-title">{title}</DialogTitle>

      <DialogContent dividers>
        <Box my={2}>
          <ClientProfileSelector
            selectedClientProfiles={selectedPatients}
            onChangeSelection={handleSelectPatients}
            queryOptions={{
              organizations: [organization],
            }}
            createOptions={{ organization }}
            readonly={Boolean(editAppointment)}
            isRequired
            // multiple
          />
        </Box>

        <Box my={2}>
          <MarketplaceSelector
            marketplaces={organization.marketplaces}
            selectedId={marketplaceId}
            onChange={handleChangeMarketplace}
            isRequired
          />
        </Box>

        <Box my={2}>
          <ProcedureSelector
            procedures={procedureDefs}
            selectedProcedures={selectedProcedures}
            onChangeSelection={(value) => setSelectedProcedures(value)}
            disabled={!marketplace}
            isRequired
          />
        </Box>

        <TimeRangeEditor
          start={startTime}
          end={endTime}
          onChangeStart={handleChangeStartTime}
          onChangeEnd={setEndTime}
          tzid={organization.tzid}
        />

        <Box my={2}>
          <PractitionerSelector
            practitioners={organization.profiles}
            selectedPractitioners={selectedPractitioners}
            onChangeSelection={(value) => setSelectedPractitioners(value)}
            label="Practitioner"
          />
        </Box>

        <Box mt={-2}>
          <LocationAutocomplete
            key={initialLocation ?? 'empty'}
            onChange={(value) => setLocation(value)}
            initialValue={initialLocation}
            disabled={!selectedPatients.length}
          />
        </Box>

        <Box my={1}>
          <TextField
            multiline
            fullWidth
            variant="outlined"
            value={notes}
            minRows={5}
            onChange={(e) => setNotes(e.target.value)}
            label="Notes"
          />
        </Box>

        {[...validationErrors, ...error].map((errorMessage) => (
          <Box key={errorMessage} mt={1}>
            <Alert variant="outlined" severity="error">
              {errorMessage}
            </Alert>
          </Box>
        ))}
      </DialogContent>

      <DialogActions>
        <Button
          disabled={submitting}
          onClick={handleClose}
          color="primary"
          size="large"
        >
          Cancel
        </Button>
        <Button
          variant="text"
          color="primary"
          size="large"
          disabled={!isValid || submitting}
          type="submit"
          onClick={handleSubmit}
        >
          {editAppointment ? 'Update' : 'Create'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
