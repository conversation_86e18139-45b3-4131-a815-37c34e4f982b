import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import Content from '@/components/Content';
import CreatePaymentAccountDialog from '@/components/CreatePaymentAccountDialog';
import FeeProfileCard from '@/components/FeeProfileCard';
import MuiLink from '@/components/Link';
import MarketplaceSettingsTabs from '@/components/MarketplaceSettingsTabs';
import PaymentAccountCard from '@/components/PaymentAccountCard';
import UserFrame from '@/components/UserFrame';
import { RoleScope, useMarketplaceQuery } from '@/generated/graphql';
import { useAuthorize } from '@/hooks/authorize';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { Box, Button, Typography } from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import Link from 'next/link';
import { useRouter } from 'next/router';

const listPermission = [
  'organizations:full',
  'organizations:list',
  {
    scope: RoleScope.Organization,
    permission: [],
  },
];

const paymentsPermission = [
  'payments:full',
  {
    scope: RoleScope.Marketplace,
    permission: ['marketplace.payments:full'],
  },
];

const feeProfilePermission = ['payments:full'];

export default function MarketplacePayments(): JSX.Element {
  const hash = usePathHash();
  const router = useRouter();

  const [canList] = useAuthorize(listPermission);
  const [canEditPayments] = useAuthorize(paymentsPermission);
  const [canEditFeeProfile] = useAuthorize(feeProfilePermission);

  const { profile } = useProfile();
  const id = router.query.id as string;

  const { data: marketplaceData } = useMarketplaceQuery({
    variables: { id },
    skip: !id || !canList,
  });

  const marketplace = marketplaceData?.marketplace;

  const basePath = `/p/${profile?.pid}/root/marketplaces/${marketplace?.id}`;
  const path = `${basePath}/payments`;
  const createDialogOpen = Object.keys(hash).includes('create');

  return (
    <UserFrame>
      <Content
        title={`Settings - ${marketplace?.name ?? 'Marketplace'}`}
        maxWidth="md"
      >
        <BreadcrumbsHeader backHref={`/p/${profile?.pid}/root/marketplaces`}>
          <MuiLink
            color="inherit"
            href={`/p/${profile?.pid}/root/marketplaces`}
          >
            Marketplaces
          </MuiLink>
          <Typography color="textPrimary">{marketplace?.name}</Typography>
        </BreadcrumbsHeader>

        <MarketplaceSettingsTabs selected="payments" path={basePath} />

        {canEditPayments && (
          <>
            <FeeProfileCard
              marketplace={marketplace}
              canEdit={canEditFeeProfile}
            />

            {marketplace?.paymentAccounts.map((account) => (
              <PaymentAccountCard
                key={account.id}
                marketplace={marketplace}
                paymentAccount={account}
              />
            ))}

            <Box mt={1} display="flex" style={{ gap: 8 }}>
              <Link href={`${path}#create`} passHref>
                <Button
                  component="a"
                  variant="outlined"
                  startIcon={<AddIcon />}
                >
                  Add account
                </Button>
              </Link>
            </Box>
          </>
        )}

        {marketplace && (
          <CreatePaymentAccountDialog
            key={`CreatePaymentAccountDialog-${createDialogOpen}`}
            marketplace={marketplace}
            open={createDialogOpen}
            onClose={() => {
              if (createDialogOpen) {
                router.replace(path);
                router.back();
              }
            }}
            allowExisting
          />
        )}
      </Content>
    </UserFrame>
  );
}
