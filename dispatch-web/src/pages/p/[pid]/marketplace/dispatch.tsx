import AppHead from '@/components/AppHead';
import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import AppointmentRequestDetailDialog from '@/components/Dispatch/AppointmentRequestDetailDialog';
import CreateAppointmentRequestDialog from '@/components/Dispatch/CreateAppointmentRequestDialog';
import DispatchGrid, {
  sanitizeColumns,
} from '@/components/Dispatch/DispatchGrid';
import UserFrame from '@/components/UserFrame';
import {
  RegularAppointmentRequestFieldsFragment,
  RoleScope,
  useAppointmentRequestQuery,
  useAppointmentRequestsQuery,
  useMarketplacesQuery,
} from '@/generated/graphql';
import { useAppointmentRequestPageInput } from '@/hooks/appointment-requests/page';
import { useAuthorize } from '@/hooks/authorize';
import { useLocalStorage } from '@/hooks/local-storage';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { useLayoutStyles } from '@/hooks/styles';
import { useApolloClient } from '@apollo/client';
import {
  Filter,
  Sorting,
  TableColumnWidthInfo,
} from '@devexpress/dx-react-grid';
import {
  Badge,
  Box,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import Button from '@material-ui/core/Button';
import AddIcon from '@material-ui/icons/Add';
import ClearIcon from '@material-ui/icons/Clear';
import FilterIcon from '@material-ui/icons/FilterAlt';
import RefreshIcon from '@material-ui/icons/Refresh';
import ResetIcon from '@material-ui/icons/RestartAlt';
import { debounce, flatten, map, uniqBy } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';

const GRID_PAGE_SIZE = 50;

const defaultFilters: Filter[] = [];
const defaultSorting: Sorting[] = [
  { columnName: 'createdAt', direction: 'desc' },
];
const defaultColumnOrder: string[] = [];
const defaultColumnWidths: TableColumnWidthInfo[] = [];

const createPermission = [
  'appointments:full',
  {
    scope: RoleScope.Marketplace,
    resourceId: null,
    permission: ['marketplace.appointments:full'],
  },
];

export default function Dispatch(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const hash = usePathHash();
  const router = useRouter();
  const apollo = useApolloClient();
  const localStorage = useLocalStorage();

  const [canCreate] = useAuthorize(createPermission);

  const { profile } = useProfile();

  const [filterMenuAnchor, setFilterMenuAnchor] = useState<HTMLElement | null>(
    null,
  );

  const [filters, setFilters] = useState(defaultFilters);
  const [sorting, setSorting] = useState(defaultSorting);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);
  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [filtersQuery, setFiltersQuery] = useState(defaultFilters);
  const [requestedPage, setRequestedPage] = useState({
    skip: 0,
    take: GRID_PAGE_SIZE,
  });
  const [stateLoaded, setStateLoaded] = useState(false);

  useEffect(() => {
    if (profile) {
      setStateLoaded(true);
      const configJSON = localStorage?.getUserItem(`${profile.pid}:dispatch`);

      if (configJSON) {
        try {
          const config = JSON.parse(configJSON);
          if (config.filters) {
            const sanitizedFilters = sanitizeColumns<Filter>(config.filters);
            setFilters(sanitizedFilters);
            setFiltersQuery(sanitizedFilters);
          }
          if (config.sorting) {
            setSorting(sanitizeColumns(config.sorting));
          }
          if (config.columnOrder) {
            setColumnOrder(sanitizeColumns(config.columnOrder));
          }
          if (config.columnWidths) {
            setColumnWidths(sanitizeColumns(config.columnWidths));
          }
        } catch {
          // invalid config
        }
      }
    }
  }, [profile, localStorage]);

  useEffect(() => {
    if (profile) {
      localStorage.setUserItem(
        `${profile.pid}:dispatch`,
        JSON.stringify({ filters, sorting, columnOrder, columnWidths }),
      );
    }
  }, [profile, filters, sorting, columnOrder, columnWidths, localStorage]);

  const { data: marketplacesData } = useMarketplacesQuery();
  const marketplaces = marketplacesData?.marketplaces;

  const organizations = useMemo(
    () => uniqBy(flatten(map(marketplaces ?? [], 'organizations')), 'name'),
    [marketplaces],
  );

  const page = useAppointmentRequestPageInput({
    offset: 0,
    limit: 2 * GRID_PAGE_SIZE,
    filters: filtersQuery,
    sorting,
  });

  const {
    data: appointmentRequestsData,
    fetchMore,
    loading,
    refetch,
  } = useAppointmentRequestsQuery({
    variables: { page },
    skip: !stateLoaded,
    notifyOnNetworkStatusChange: true,
  });

  const { data: appointmentRequestData } = useAppointmentRequestQuery({
    variables: { id: hash.id as string },
    skip: !hash.id,
  });

  const appointmentRequests = appointmentRequestsData?.appointmentRequests;

  const selectedRequest = hash.id
    ? appointmentRequestData?.appointmentRequest
    : null;

  const path = `/p/${profile?.pid}/marketplace/dispatch`;

  const resetCache = useCallback(() => {
    apollo.cache.evict({ fieldName: 'appointmentRequests' });

    setRequestedPage({
      skip: 0,
      take: GRID_PAGE_SIZE,
    });
  }, [apollo, setRequestedPage]);

  const setFiltersQueryDebounced = useMemo(
    () =>
      debounce((updatedFilters: Filter[]) => {
        resetCache();
        setFiltersQuery(updatedFilters);
      }, 500),
    [setFiltersQuery, resetCache],
  );

  const handleFiltersChange = (updatedFilters: Filter[]) => {
    setFilters(updatedFilters);
    setFiltersQueryDebounced(updatedFilters);
  };

  const handleSortingChange = (updatedSorting: Sorting[]) => {
    resetCache();
    setSorting(updatedSorting);
  };

  const handleColumnOrderChange = (nextOrder: string[]) => {
    setColumnOrder(nextOrder);
  };

  const handleColumnWidthsChange = (
    nextColumnWidths: TableColumnWidthInfo[],
  ) => {
    setColumnWidths(nextColumnWidths);
  };

  const handleSelect = (
    appointmentRequest?: RegularAppointmentRequestFieldsFragment,
  ) => {
    if (appointmentRequest) {
      router.push(`${path}#id=${appointmentRequest.id}`);
    }
  };

  const handleCloseFilterMenu = () => {
    setFilterMenuAnchor(null);
  };

  const handleLoadMore = (skip: number) => {
    setRequestedPage({ skip, take: GRID_PAGE_SIZE });
  };

  const reset = () => {
    resetCache();
    refetch();
  };

  useEffect(() => {
    const { skip, take } = requestedPage;
    const cachedCount = appointmentRequests?.data.length ?? 0;

    if (
      appointmentRequests &&
      appointmentRequests.totalCount > cachedCount &&
      skip < appointmentRequests.totalCount &&
      skip + take > cachedCount
    ) {
      fetchMore({
        variables: {
          page: {
            ...page,
            offset: cachedCount,
            limit: GRID_PAGE_SIZE,
          },
        },
      });
    }
  }, [requestedPage, appointmentRequests, page, fetchMore]);

  return (
    <UserFrame fixed>
      <AppHead title="Dispatch" />
      <Box height="100%" display="flex" flexDirection="column">
        <Toolbar>
          <Box display="flex" flexGrow="1" alignItems="center">
            <Box mt={2}>
              <BreadcrumbsHeader>
                <Typography color="textPrimary">
                  Appointment Requests
                </Typography>
              </BreadcrumbsHeader>
            </Box>
          </Box>

          <IconButton
            color="default"
            onClick={() => reset()}
            disabled={loading}
          >
            <RefreshIcon />
          </IconButton>

          <IconButton
            color="default"
            onClick={(event) => setFilterMenuAnchor(event.currentTarget)}
            disabled={loading}
          >
            <Badge
              variant="dot"
              color="secondary"
              badgeContent={filters.length}
            >
              <FilterIcon />
            </Badge>
          </IconButton>

          {canCreate &&
            (fullScreen ? (
              <Link href={`${path}#create`} passHref>
                <IconButton color="primary">
                  <AddIcon />
                </IconButton>
              </Link>
            ) : (
              <Link href={`${path}#create`} passHref>
                <Button
                  color="primary"
                  variant="contained"
                  size="medium"
                  style={{ marginLeft: '1rem' }}
                >
                  Create Request
                </Button>
              </Link>
            ))}
        </Toolbar>

        <Box flexGrow="1" display="flex" minHeight={0}>
          <DispatchGrid
            appointmentRequests={appointmentRequests?.data ?? []}
            totalCount={appointmentRequests?.totalCount ?? 0}
            loading={loading}
            filters={filters}
            sorting={sorting}
            columnOrder={columnOrder}
            columnWidths={columnWidths}
            onFiltersChange={handleFiltersChange}
            onSortingChange={handleSortingChange}
            onColumnOrderChange={handleColumnOrderChange}
            onColumnWidthsChange={handleColumnWidthsChange}
            onLoadMore={handleLoadMore}
            onSelect={handleSelect}
            organizationsFilterList={organizations}
            marketplacesFilterList={marketplaces}
          />
        </Box>
      </Box>

      {Object.keys(hash).includes('create') && (
        <CreateAppointmentRequestDialog
          marketplaces={marketplaces ?? []}
          title="Create appointment request"
          open={Object.keys(hash).includes('create')}
          onClose={() => {
            if (Object.keys(hash).includes('create')) {
              router.replace(path);
              router.back();
            }
          }}
          onCreated={() => reset()}
        />
      )}

      <AppointmentRequestDetailDialog
        key={`detail-${selectedRequest?.id}`}
        path={`${path}#id=${selectedRequest?.id}`}
        appointmentRequest={selectedRequest}
        open={Boolean(selectedRequest)}
        onClose={() => {
          if (selectedRequest) {
            router.replace(path);
          }
        }}
      />

      <Menu
        open={Boolean(filterMenuAnchor)}
        onClose={() => handleCloseFilterMenu()}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        getContentAnchorEl={null}
        anchorEl={filterMenuAnchor}
        keepMounted
      >
        <MenuItem
          disabled={!filters.length}
          onClick={() => {
            handleCloseFilterMenu();
            handleFiltersChange([]);
          }}
        >
          <ClearIcon className={layoutClasses.menuIcon} />
          Clear filters
        </MenuItem>

        <Divider className={layoutClasses.menuDivider} />

        <MenuItem
          onClick={() => {
            handleCloseFilterMenu();
            handleFiltersChange(defaultFilters);
            handleSortingChange(defaultSorting);
            handleColumnOrderChange(defaultColumnOrder);
            handleColumnWidthsChange(defaultColumnWidths);
          }}
        >
          <ResetIcon className={layoutClasses.menuIcon} />
          Reset grid
        </MenuItem>
      </Menu>
    </UserFrame>
  );
}
