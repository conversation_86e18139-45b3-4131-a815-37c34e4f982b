import AppHead from '@/components/AppHead';
import AppointmentDetailDialog from '@/components/Appointment/AppointmentDetailDialog';
import AppointmentsGrid, {
  sanitizeColumns,
} from '@/components/Appointment/AppointmentsGrid';
import CreateAppointmentDialog from '@/components/Appointment/CreateAppointmentDialog';
import BreadcrumbsHeader from '@/components/BreadcrumbsHeader';
import UserFrame from '@/components/UserFrame';
import {
  RegularAppointmentFieldsFragment,
  RoleScope,
  useAppointmentQuery,
  useOrganizationAppointmentsQuery,
  useOrganizationQuery,
} from '@/generated/graphql';
import { useAppointmentPageInput } from '@/hooks/appointments/page';
import { useAuthorize } from '@/hooks/authorize';
import { useLocalStorage } from '@/hooks/local-storage';
import { useDenormalizedOrganization } from '@/hooks/organization';
import { useProfile } from '@/hooks/profile';
import { usePathHash } from '@/hooks/router';
import { useLayoutStyles } from '@/hooks/styles';
import { shrinkString } from '@/utils/common';
import { useApolloClient } from '@apollo/client';
import {
  Filter,
  Sorting,
  TableColumnWidthInfo,
} from '@devexpress/dx-react-grid';
import {
  Badge,
  Box,
  Divider,
  IconButton,
  Menu,
  MenuItem,
  Toolbar,
  Typography,
  useMediaQuery,
  useTheme,
} from '@material-ui/core';
import Button from '@material-ui/core/Button';
import { makeStyles } from '@material-ui/core/styles';
import AddIcon from '@material-ui/icons/Add';
import ClearIcon from '@material-ui/icons/Clear';
import FilterIcon from '@material-ui/icons/FilterAlt';
import RefreshIcon from '@material-ui/icons/Refresh';
import ResetIcon from '@material-ui/icons/RestartAlt';
import GetAppIcon from '@material-ui/icons/GetApp';
import clsx from 'clsx';
import { debounce } from 'lodash';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';

const GRID_PAGE_SIZE = 50;
const CALENDAR_PAGE_SIZE = 1000;

const defaultFilters: Filter[] = [];
const defaultSorting: Sorting[] = [
  { columnName: 'createdAt', direction: 'desc' },
];
const defaultColumnOrder: string[] = [];
const defaultColumnWidths: TableColumnWidthInfo[] = [];

const createPermission = [
  'appointments:full',
  {
    scope: RoleScope.Organization,
    permission: ['organization.appointments:full'],
  },
];

const useStyles = makeStyles((theme) => ({
  toolbar: {
    display: 'flex',
    alignItems: 'center',
    flex: '1',
    flexWrap: 'nowrap',
    [theme.breakpoints.down('xs')]: {
      flexWrap: 'wrap',
    },
  },
  calendarToolbar: {
    borderBottom: `1px solid ${theme.palette.divider}`,
    [theme.breakpoints.down('xs')]: {
      minHeight: 112,
      maxHeight: 112,
    },
  },
  toolbarLabel: {
    marginTop: theme.spacing(2),
    [theme.breakpoints.down('xs')]: {
      width: '100%',
    },
  },
  toolbarActions: {
    display: 'flex',
    flex: '1',
    alignItems: 'center',
    [theme.breakpoints.down('xs')]: {
      margin: theme.spacing(-1, 0, 1),
    },
  },
  toolbarActionsLeft: {
    display: 'flex',
    flex: '1',
    alignItems: 'center',
    margin: theme.spacing(0, 2),
    [theme.breakpoints.down('xs')]: {
      margin: theme.spacing(0),
    },
  },
}));

export default function Appointments(): JSX.Element {
  const layoutClasses = useLayoutStyles();
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('xs'));
  const hash = usePathHash();
  const router = useRouter();
  const apollo = useApolloClient();
  const classes = useStyles();
  const localStorage = useLocalStorage();

  const [canCreate] = useAuthorize(createPermission);

  const { profile } = useProfile();
  const id = profile?.organization?.id;

  const [filterMenuAnchor, setFilterMenuAnchor] = useState<HTMLElement | null>(
    null,
  );

  const [view, setView] = useState('grid');
  const [filters, setFilters] = useState(defaultFilters);
  const [sorting, setSorting] = useState(defaultSorting);
  const [columnOrder, setColumnOrder] = useState(defaultColumnOrder);
  const [columnWidths, setColumnWidths] = useState(defaultColumnWidths);
  const [filtersQuery, setFiltersQuery] = useState(defaultFilters);
  const [dateRange] = useState<[Date, Date] | null>(null);
  const [requestedPage, setRequestedPage] = useState({
    skip: 0,
    take: GRID_PAGE_SIZE,
  });
  const [stateLoaded, setStateLoaded] = useState(false);

  useEffect(() => {
    if (profile) {
      setStateLoaded(true);
      const configJSON = localStorage?.getUserItem(
        `${profile.pid}:appointments`,
      );

      if (configJSON) {
        try {
          const config = JSON.parse(configJSON);
          if (config.view) {
            setView(config.view === 'calendar' ? 'calendar' : 'grid');
          }
          if (config.filters) {
            const sanitizedFilters = sanitizeColumns<Filter>(config.filters);
            setFilters(sanitizedFilters);
            setFiltersQuery(sanitizedFilters);
          }
          if (config.sorting) {
            setSorting(sanitizeColumns(config.sorting));
          }
          if (config.columnOrder) {
            setColumnOrder(sanitizeColumns(config.columnOrder));
          }
          if (config.columnWidths) {
            setColumnWidths(sanitizeColumns(config.columnWidths));
          }
        } catch {
          // invalid config
        }
      }
    }
  }, [profile, localStorage]);

  useEffect(() => {
    if (profile) {
      localStorage.setUserItem(
        `${profile.pid}:appointments`,
        JSON.stringify({ view, filters, sorting, columnOrder, columnWidths }),
      );
    }
  }, [
    profile,
    view,
    filters,
    sorting,
    columnOrder,
    columnWidths,
    localStorage,
  ]);

  const { data: organizationData } = useOrganizationQuery({
    variables: { id: id as string },
    skip: !id,
  });

  const organization = useDenormalizedOrganization(
    organizationData?.organization,
  );

  const page = useAppointmentPageInput({
    offset: 0,
    limit: dateRange ? CALENDAR_PAGE_SIZE : 2 * GRID_PAGE_SIZE,
    filters: filtersQuery,
    sorting,
    dateRange,
  });

  const {
    data: organizationAppointmentsData,
    fetchMore,
    loading,
    refetch,
  } = useOrganizationAppointmentsQuery({
    variables: {
      id: id as string,
      page,
    },
    skip: !stateLoaded || !id || (view === 'calendar' && !dateRange),
    notifyOnNetworkStatusChange: true,
    // fetchPolicy
  });

  const appointments = organizationAppointmentsData?.organization?.appointments;

  const { data: appointmentData } = useAppointmentQuery({
    variables: { id: hash.id as string },
    skip: !hash.id,
  });

  const selectedAppointment = hash.id ? appointmentData?.appointment : null;

  // For copying appointments
  const { data: copyAppointmentData } = useAppointmentQuery({
    variables: { id: hash.copy as string },
    skip: !hash.copy,
  });

  const copyAppointment = hash.copy ? copyAppointmentData?.appointment : null;

  const path = `/p/${profile?.pid}/appointments`;

  const resetCache = useCallback(() => {
    apollo.cache.evict({ id: `Organization:${id}`, fieldName: 'appointments' });

    setRequestedPage({
      skip: 0,
      take: GRID_PAGE_SIZE,
    });
  }, [id, apollo, setRequestedPage]);

  const setFiltersQueryDebounced = useMemo(
    () =>
      debounce((updatedFilters: Filter[]) => {
        resetCache();
        setFiltersQuery(updatedFilters);
      }, 500),
    [setFiltersQuery, resetCache],
  );

  const handleFiltersChange = (updatedFilters: Filter[]) => {
    setFilters(updatedFilters);
    setFiltersQueryDebounced(updatedFilters);
  };

  const handleSortingChange = (updatedSorting: Sorting[]) => {
    resetCache();
    setSorting(updatedSorting);
  };

  const handleColumnOrderChange = (nextOrder: string[]) => {
    setColumnOrder(nextOrder);
  };

  const handleColumnWidthsChange = (
    nextColumnWidths: TableColumnWidthInfo[],
  ) => {
    setColumnWidths(nextColumnWidths);
  };

  const handleSelect = (appointment?: RegularAppointmentFieldsFragment) => {
    if (appointment) {
      router.push(`${path}#id=${appointment.id}`);
    }
  };

  const handleCloseFilterMenu = () => {
    setFilterMenuAnchor(null);
  };

  const handleLoadMore = (skip: number) => {
    setRequestedPage({ skip, take: GRID_PAGE_SIZE });
  };

  const reset = () => {
    resetCache();
    refetch();
  };

  const handleCopyAppointment = (appointmentId: string) => {
    router.replace(`${path}#copy=${appointmentId}&create=true`);
  };

  useEffect(() => {
    const { skip, take } = requestedPage;
    const cachedCount = appointments?.data.length ?? 0;

    if (
      appointments &&
      appointments.totalCount > cachedCount &&
      skip < appointments.totalCount &&
      skip + take > cachedCount
    ) {
      fetchMore({
        variables: {
          page: {
            ...page,
            offset: cachedCount,
            limit: GRID_PAGE_SIZE,
          },
        },
      });
    }
  }, [requestedPage, appointments, page, fetchMore]);

  return (
    <UserFrame fixed>
      <AppHead title="Appointments" />
      <Box height="100%" display="flex" flexDirection="column">
        <Toolbar
          className={clsx({
            [classes.calendarToolbar]: view === 'calendar',
          })}
        >
          <div className={classes.toolbar}>
            <div className={classes.toolbarLabel}>
              <BreadcrumbsHeader>
                <Typography color="textSecondary">
                  {shrinkString(organization?.name, 40)}
                </Typography>
                <Typography color="textPrimary">Appointments</Typography>
              </BreadcrumbsHeader>
            </div>

            <div className={classes.toolbarActions}>
              <div className={classes.toolbarActionsLeft} />

              <IconButton
                color="default"
                onClick={() => reset()}
                disabled={loading}
              >
                <RefreshIcon />
              </IconButton>
              <Link href={`/p/${profile?.pid}/reports`} passHref>
                <IconButton color="default">
                  <GetAppIcon />
                </IconButton>
              </Link>
              {view === 'grid' && (
                <IconButton
                  color="default"
                  onClick={(event) => setFilterMenuAnchor(event.currentTarget)}
                  disabled={loading}
                >
                  <Badge
                    variant="dot"
                    color="secondary"
                    overlap="rectangular"
                    badgeContent={filters.length}
                  >
                    <FilterIcon />
                  </Badge>
                </IconButton>
              )}

              {canCreate &&
                (fullScreen ? (
                  <div>
                    <Link href={`${path}#create`} passHref>
                      <IconButton color="primary">
                        <AddIcon />
                      </IconButton>
                    </Link>
                  </div>
                ) : (
                  <Link href={`${path}#create`} passHref>
                    <Button
                      color="primary"
                      variant="contained"
                      size="medium"
                      style={{ marginLeft: '1rem' }}
                    >
                      <span style={{ whiteSpace: 'nowrap' }}>
                        Create Appointment
                      </span>
                    </Button>
                  </Link>
                ))}
            </div>
          </div>
        </Toolbar>

        {Boolean(organization) && (
          <Box flexGrow="1" display="flex" minHeight={0}>
            <AppointmentsGrid
              appointments={appointments?.data ?? []}
              totalCount={appointments?.totalCount ?? 0}
              loading={loading}
              filters={filters}
              sorting={sorting}
              columnOrder={columnOrder}
              columnWidths={columnWidths}
              onFiltersChange={handleFiltersChange}
              onSortingChange={handleSortingChange}
              onColumnOrderChange={handleColumnOrderChange}
              onColumnWidthsChange={handleColumnWidthsChange}
              onLoadMore={handleLoadMore}
              onSelect={handleSelect}
              tzid={profile?.organization?.tzid}
            />
          </Box>
        )}
      </Box>

      {Object.keys(hash).includes('create') && !!organization && (
        <CreateAppointmentDialog
          organization={organization}
          title={hash.copy ? 'Copy Appointment' : 'Create Appointment'}
          open={Object.keys(hash).includes('create')}
          onClose={() => {
            if (Object.keys(hash).includes('create')) {
              // router.replace(path);
              router.back();
            }
          }}
          onCreated={() => {
            reset();
          }}
          editAppointment={hash.copy ? copyAppointment : null}
          isCopying={Boolean(hash.copy)}
        />
      )}

      <AppointmentDetailDialog
        key={`appointment-${hash.id}`}
        appointment={selectedAppointment}
        organization={organization}
        open={Boolean(hash.id)}
        onClose={() => {
          if (hash.id) {
            router.replace(path);
            // router.back();
          }
        }}
        onCopyAppointment={() => {
          if (selectedAppointment) {
            handleCopyAppointment(selectedAppointment.id);
          }
        }}
      />

      <Menu
        open={Boolean(filterMenuAnchor)}
        onClose={() => handleCloseFilterMenu()}
        anchorOrigin={{ vertical: 'top', horizontal: 'left' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        getContentAnchorEl={null}
        anchorEl={filterMenuAnchor}
        keepMounted
      >
        <MenuItem
          disabled={!filters.length}
          onClick={() => {
            handleCloseFilterMenu();
            handleFiltersChange([]);
          }}
        >
          <ClearIcon className={layoutClasses.menuIcon} />
          Clear filters
        </MenuItem>

        <Divider className={layoutClasses.menuDivider} />

        <MenuItem
          onClick={() => {
            handleCloseFilterMenu();
            handleFiltersChange(defaultFilters);
            handleSortingChange(defaultSorting);
            handleColumnOrderChange(defaultColumnOrder);
            handleColumnWidthsChange(defaultColumnWidths);
          }}
        >
          <ResetIcon className={layoutClasses.menuIcon} />
          Reset grid
        </MenuItem>
      </Menu>
    </UserFrame>
  );
}
