{"name": "dispatch-web", "version": "1.0.0", "private": true, "scripts": {"prettier": "npx prettier -w .", "lint": "npx eslint --color --ext .js,.jsx,.ts,.tsx .", "generate": "graphql-codegen --config .graphqlrc.yml", "dev": "next", "build": "next build", "start": "next start -p $PORT"}, "dependencies": {"@apollo/client": "^3.6.9", "@date-io/dayjs": "^1.3.13", "@devexpress/dx-react-core": "^2.7.6", "@devexpress/dx-react-grid": "^2.7.6", "@devexpress/dx-react-grid-material-ui": "^2.7.6", "@devexpress/dx-react-scheduler": "^2.7.6", "@devexpress/dx-react-scheduler-material-ui": "^2.7.6", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^5.0.0-beta.5", "@material-ui/lab": "^4.0.0-alpha.57", "@material-ui/pickers": "^3.3.10", "@react-google-maps/api": "^2.12.1", "chart.js": "^4.3.3", "clsx": "^1.2.1", "dayjs": "^1.11.4", "filesize": "^8.0.7", "formik": "^2.2.9", "formik-material-ui": "^3.0.1", "graphql": "^15.8.0", "lodash": "^4.17.21", "next": "^12.3.4", "notistack": "^1.0.10", "react": "^17.0.2", "react-chartjs-2": "^5.2.0", "react-colorful": "^5.6.0", "react-dom": "^17.0.2", "react-drag-drop-files": "^2.3.4", "react-sortable-hoc": "^2.0.0", "sharp": "^0.30.7", "use-places-autocomplete": "^1.11.0", "validator": "^13.7.0", "yup": "^0.29.3"}, "devDependencies": {"@graphql-codegen/cli": "^2.11.2", "@graphql-codegen/introspection": "^2.2.0", "@graphql-codegen/typescript": "^2.7.2", "@graphql-codegen/typescript-graphql-files-modules": "^2.2.0", "@graphql-codegen/typescript-operations": "^2.5.2", "@graphql-codegen/typescript-react-apollo": "^3.3.2", "@material-ui/codemod": "^4.5.1", "@next/eslint-plugin-next": "^11.1.4", "@types/google.maps": "^3.49.2", "@types/lodash": "^4.14.182", "@types/node": "^14.18.22", "@types/react": "^17.0.48", "@types/react-dom": "^17.0.17", "@types/source-map-support": "^0.5.4", "@types/validator": "^13.7.4", "@types/yup": "^0.29.14", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "aws-cdk-lib": "^2.140.0", "cdk-nextjs-standalone": "^4.0.0-beta.29", "constructs": "^10.3.0", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-airbnb-typescript": "^14.0.2", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.7.1", "source-map-support": "^0.5.21", "ts-node": "^10.9.1", "typescript": "^4.7.4"}}